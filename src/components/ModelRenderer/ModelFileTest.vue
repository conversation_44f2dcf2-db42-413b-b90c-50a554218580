<script lang="ts" setup>
import {
  getObjectSize,
  BVHRaycastTool,
  useRendererContext,
  isMesh,
  RaycastEvent,
} from '../ThreeJs';
import { ModelFile } from './ModelFile';
import { watchImmediate } from '@vueuse/core';
import { Color, Mesh, MeshStandardMaterial, type Object3D } from 'three';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { useDisposableStack } from '../ThreeJs/composables';
import { V1CommonFileUploadDownloadFileIdPost } from '/@/api/cddc.req';
import { message } from '@geega-ui-plus/ant-design-vue';

export interface ModelFileProps {
  fileId?: string;
  markedParts?: string[];
  modelCache?: Record<string, File>;
}

const props = defineProps<ModelFileProps>();
const emit = defineEmits<{
  selected: [event: RaycastEvent];
  beforeLoadingModel: [];
  loaded: [scene: ModelFile];
}>();

const ctx = useRendererContext();

const disposableStack = useDisposableStack();

const modelFile = new ModelFile();
disposableStack.add(modelFile);

const modelMat = new MeshStandardMaterial({
  color: new Color().setRGB(42 / 255, 42 / 255, 42 / 255),
  metalness: 0.45,
  roughness: 0.35,
});
disposableStack.add(modelMat);

const highlightModelMat = new MeshStandardMaterial({
  color: new Color('#ff0000'),
  metalness: 0.45,
  roughness: 0.35,
});
disposableStack.add(highlightModelMat);

const markModelMat = new MeshStandardMaterial({
  color: new Color('#00ff00'),
  metalness: 0.45,
  roughness: 0.35,
});
disposableStack.add(markModelMat);

const state = {
  selectedPart: null as Mesh | null | undefined,
};

const raycastTool = new BVHRaycastTool(ctx, { enabledEvents: ['click'], debounce: 100 });
disposableStack.add(raycastTool);

raycastTool.addEventListener('pointermove', (evt) => {
  const mesh = evt.mesh;

  const outlinePass = ctx.getData<OutlinePass>('outlinePass')!;

  outlinePass.selectedObjects = [mesh].filter((n) => n != null);
});

raycastTool.addEventListener('click', (evt) => {
  const mesh = evt.mesh;

  const oldSelectedPart = state.selectedPart;

  state.selectedPart = mesh;

  if (oldSelectedPart) {
    oldSelectedPart.material = getMaterial(oldSelectedPart);
  }

  if (mesh) {
    mesh.material = getMaterial(mesh);
  }

  emit('selected', evt);
});

watchImmediate(() => props.fileId, reloadModelFile);

watchImmediate(() => props.markedParts, updateModelPartMaterials);

async function reloadModelFile() {
  const fileId = props.fileId;

  if (fileId) {
    clearOldModel();

    const url = await V1CommonFileUploadDownloadFileIdPost({ fileId });

    emit('beforeLoadingModel');

    try {
      const data = props.modelCache?.[fileId];
      const dataBuffer = await data?.arrayBuffer();
      await modelFile.loadModel(url, dataBuffer);
    } catch (error) {
      emit('loaded', modelFile);

      console.error(error);
      message.error('模型加载失败！');
      return;
    }

    if (modelFile.model) {
      updateModelMaterial(modelFile.model);
    }
    ctx.scene.add(modelFile);

    raycastTool.objects = [modelFile];

    const modelSize = getObjectSize(modelFile);
    modelFile.position.set(0, modelSize.y / 2, 0);

    const modelLength = modelSize.length() / 1.5;

    ctx.camera.position.set(modelLength, modelLength, modelLength);
    ctx.camera.lookAt(modelFile.position);

    emit('loaded', modelFile);
  } else {
    clearOldModel();
  }
}

function clearOldModel() {
  raycastTool.objects = [];

  modelFile.clear();
}

function updateModelPartMaterials() {
  state.selectedPart = null;

  modelFile.traverse((item) => {
    if (!isMesh(item)) {
      return;
    }

    item.material = getMaterial(item);
  });
}

function getMaterial(mesh: Mesh) {
  const shouldMark = props.markedParts?.includes(mesh.name);
  const shouldHighlight = state.selectedPart === mesh;
  if (shouldHighlight) {
    return highlightModelMat;
  }

  if (shouldMark) {
    return markModelMat;
  }

  return modelMat;
}

function updateModelMaterial(scene: Object3D) {
  scene.traverse((item) => {
    if (item instanceof Mesh) {
      item.material = modelMat;
    }
  });

  return scene;
}
</script>

<template>
  <slot></slot>
</template>

<style lang="less" scoped></style>
