<script lang="ts" setup>
import {
  ACESFilmicToneMapping,
  AmbientLight,
  Color,
  DirectionalLight,
  ********************************,
  Mesh,
  MeshStandardMaterial,
  Object3D,
  PlaneGeometry,
  TextureLoader,
  Vector3,
} from 'three';
import { SimpleThree, ThreeContextProvider } from '../ThreeJs';
import { OutlinePass } from 'three/examples/jsm/postprocessing/OutlinePass';
import { OutputPass } from 'three/examples/jsm/postprocessing/OutputPass';
import { ShaderPass } from 'three/examples/jsm/postprocessing/ShaderPass';
import { FXAAShader } from 'three/examples/jsm/shaders/FXAAShader';
import DevInspector from './Devtools/DevInspector.vue';
import { shallowRef } from 'vue';

export interface ModelRendererProps {
  inspector?: boolean;
}

const props = defineProps<ModelRendererProps>();

const rendererCtx = shallowRef<SimpleThree>();

function setup(ctx: SimpleThree) {
  rendererCtx.value = ctx;
  setupOutlineEffect(ctx);

  ctx.orbit.maxPolarAngle = Math.PI / 2;
  ctx.orbit.screenSpacePanning = false;

  ctx.camera.position.set(6, 6, 6);
  ctx.camera.lookAt(new Vector3());

  // ctx.$r.shadowMap.enabled = true;

  {
    const envLight = new AmbientLight(0x404040, 10);
    ctx.scene.add(envLight);
  }

  // addTestPanel(ctx);
  // addSkyBox(ctx);
  addLights(ctx);
}

function setupOutlineEffect(ctx: SimpleThree) {
  const outlinePass = new OutlinePass(ctx.size, ctx.scene, ctx.camera);
  ctx.composer.addPass(outlinePass);
  ctx.setData('outlinePass', outlinePass);

  outlinePass.edgeStrength = 3.0;
  outlinePass.edgeGlow = 0;
  outlinePass.edgeThickness = 1;
  outlinePass.pulsePeriod = 0;
  outlinePass.usePatternTexture = false;
  outlinePass.visibleEdgeColor.set('#00ff00');
  outlinePass.hiddenEdgeColor.set('#ff0000');

  ctx.composer.addPass(new OutputPass());
  const effectFXAA = new ShaderPass(FXAAShader);
  effectFXAA.uniforms['resolution'].value.set(1 / ctx.size.width, 1 / ctx.size.height);

  ctx.addEventListener('resize', (evt) => {
    effectFXAA.uniforms['resolution'].value.set(1 / evt.size.width, 1 / evt.size.height);
  });

  ctx.composer.addPass(effectFXAA);
}

async function addSkyBox(ctx: SimpleThree) {
  // add skybox
  const loader = new TextureLoader();
  const texture = await loader.loadAsync('/test/Skyboxes/BlueSkySkybox.png');
  texture.mapping = ********************************;

  ctx.scene.background = texture;

  ctx.$r.toneMapping = ACESFilmicToneMapping;
  ctx.$r.toneMappingExposure = 1;
}

function addTestPanel(ctx: SimpleThree) {
  const panel = new PlaneGeometry(100, 100);
  const mesh = new Mesh(panel, new MeshStandardMaterial({ color: '#ffffff' }));
  mesh.receiveShadow = true;

  panel.rotateX(-Math.PI / 2);
  ctx.scene.add(mesh);
}

function addLights(ctx: SimpleThree) {
  const lightsGroup = new Object3D();
  ctx.scene.add(lightsGroup);

  // const ambientLight = new AmbientLight(0x000000, 1);
  // lightsGroup.add(ambientLight);

  const lightColor = new Color(0xffffff);
  const intensity = 1;

  // 创建 6 面光照
  createLight(new Vector3(1, 0, 0));
  createLight(new Vector3(-1, 0, 0));

  createLight(new Vector3(0, 1, 0), true);
  createLight(new Vector3(0, -1, 0));

  createLight(new Vector3(0, 0, 1));
  createLight(new Vector3(0, 0, -1));

  return;

  function createLight(pos: Vector3, enableShadow = false) {
    const light = new DirectionalLight(lightColor, intensity);
    light.position.copy(pos.multiplyScalar(20));

    light.castShadow = enableShadow;

    lightsGroup.add(light);

    return light;
  }
}
</script>

<template>
  <div class="flex size-full">
    <ThreeContextProvider
      class="flex-1"
      :setup="setup"
      :initOption="{ hideViewHelper: true, disableCSS2D: true }"
    >
      <slot />
    </ThreeContextProvider>
    <div v-if="inspector && rendererCtx" class="inspector-wrapper">
      <DevInspector :context="rendererCtx" />
    </div>
  </div>
</template>

<style lang="less" scoped>
.inspector-wrapper {
  width: 240px;
}
</style>
