import { ref } from 'vue';

type ErrorType = 1 | 2 | 3 | 4 | 8;

interface ErrorSound {
  type: ErrorType;
  description: string;
  file: string;
}

const ERROR_SOUNDS: Record<ErrorType, ErrorSound> = {
  1: { type: 1, description: '未双手作业', file: '/resource/error1.mp3' },
  2: { type: 2, description: '未垂直作业面', file: '/resource/error2.mp3' },
  3: { type: 3, description: '拧紧未贴合', file: '/resource/error3.mp3' },
  4: { type: 4, description: '拧紧枪未亮绿灯', file: '/resource/error4.mp3' },
  8: { type: 8, description: '未正确带手套', file: '/resource/error8.mp3' },
};

const MORE_ERROR_SOUND = '/resource/moreError.mp3';

export const useErrorSound = () => {
  const audioElements = ref<Map<string, HTMLAudioElement>>(new Map());

  // 初始化音频元素
  const initAudioElements = () => {
    // 创建单个错误的音频元素
    Object.values(ERROR_SOUNDS).forEach((sound) => {
      const audio = new Audio(sound.file);
      audio.preload = 'auto';
      audioElements.value.set(sound.file, audio);
    });

    // 创建多个错误的音频元素
    const moreErrorAudio = new Audio(MORE_ERROR_SOUND);
    moreErrorAudio.preload = 'auto';
    audioElements.value.set(MORE_ERROR_SOUND, moreErrorAudio);
  };

  // 播放错误提示音
  const playErrorSound = (actionLabels?: { actionType: ErrorType }[]) => {
    if (!actionLabels?.length) return;

    // 确保音频元素已初始化
    if (audioElements.value.size === 0) {
      initAudioElements();
    }

    // 如果有多个错误，播放多错误提示音
    if (actionLabels.length > 1) {
      const audio = audioElements.value.get(MORE_ERROR_SOUND);
      audio?.play().catch((error) => {
        console.warn('播放多错误提示音失败:', error);
      });
      return;
    }

    // 单个错误时，播放对应提示音
    const errorSound = ERROR_SOUNDS[actionLabels[0].actionType];
    if (errorSound) {
      const audio = audioElements.value.get(errorSound.file);
      audio?.play().catch((error) => {
        console.warn(`播放错误提示音(${errorSound.description})失败:`, error);
      });
    }
  };

  return {
    playErrorSound,
  };
};
