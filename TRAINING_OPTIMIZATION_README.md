# 训练页面顶部信息优化

## 功能概述

为训练页面顶部新增了声音切换和模式切换功能，提升用户体验和训练效果。

## 新增功能

### 1. 声音控制系统
- **位置**: 训练页面顶部，ProjectSelector组件的插槽中
- **组成**:
  - **声音开关**: 控制声音的开启/关闭
    - 开启时显示 🔊 图标
    - 关闭时显示 🔇 图标
  - **声音类型选择**: 下拉选择器，选择声音风格
    - **理性**: 专业、严肃的提示音风格
    - **活泼**: 轻松、友好的提示音风格
    - 只有在声音开启时才可选择
- **状态保存**: 声音开关和类型设置都会保存到本地存储
- **交互反馈**: 切换时显示相应的成功提示信息

### 2. 显示模式选择
- **位置**: 训练页面顶部，ProjectSelector组件的插槽中，声音控制旁边
- **功能**: 界面显示模式选择（不带开关）
- **图标**: 📄 文件同步图标
- **模式选项**:
  - **简约**: 简洁的界面显示模式
  - **专业**: 详细的专业界面显示模式
- **状态保存**: 模式选择会保存到本地存储
- **交互反馈**: 切换时显示当前选择的模式

## 技术实现

### 前端组件
- 使用 Ant Design Vue 的 `a-switch` 组件实现声音开关
- 使用 `a-select` 组件实现模式选择
- 响应式设计，适配不同屏幕尺寸

### 状态管理
- 使用 Vue 3 Composition API 管理组件状态
- 通过 `createLocalStorage` 实现设置的持久化存储
- 组件挂载时自动初始化用户设置

### 声音控制集成
- 集成现有的 `useErrorSound` composable
- 根据声音开关状态控制错误提示音播放
- 支持多种错误类型的音频提示

## 样式设计

### 布局
- 控制组件通过ProjectSelector组件的插槽机制集成到顶部区域
- 利用ProjectSelector的现有布局结构，确保与设计图一致
- 控制组件之间使用适当的间距，保持视觉平衡

### 主题适配
- 深色主题下的组件样式优化
- 开关和选择器使用半透明背景，与页面整体风格保持一致
- 文字颜色为白色，确保在深色背景下的可读性

### 响应式设计
- 小屏幕下控制组件会调整布局和字体大小
- 保证在不同设备上的良好显示效果

## 用户体验优化

1. **直观的控制界面**: 清晰的标签和易于理解的控制组件
2. **即时反馈**: 设置更改时提供即时的视觉和文字反馈
3. **设置持久化**: 用户设置自动保存，提升使用便利性
4. **无缝集成**: 新功能与现有界面完美融合，不影响原有操作流程

## 使用说明

1. **声音控制系统**:
   - **声音开关**: 🔊/🔇 图标 + 开关控件
     - 开启时显示音量图标，关闭时显示静音图标
     - 控制是否播放训练过程中的错误提示音
   - **声音类型选择**: 下拉选择器
     - **理性**: 播放专业、严肃的错误提示音
     - **活泼**: 播放轻松、友好的错误提示音
     - 只有在声音开启时才可选择，关闭时为禁用状态
   - 训练中出现错误时会根据声音开关和类型设置播放相应的提示音

2. **显示模式选择**:
   - 📄 图标 + 下拉选择器，可以选择界面显示模式
   - **简约模式**: 简洁的界面显示，突出核心信息
   - **专业模式**: 详细的专业界面，显示更多技术信息
   - 模式切换会立即生效，影响整体界面的信息密度

## 后续扩展

该功能为训练系统的设置功能奠定了基础，后续可以考虑添加：
- 音量调节功能
- 更多训练模式选项
- 个性化设置面板
- 快捷键支持
