# useErrorSound 使用说明

## 概述

`useErrorSound` 是一个用于处理训练过程中错误提示音的 Vue 3 Composable。它根据后端提供的错误配置，支持多种错误类型的音频提示。

## 支持的错误类型

### 基础作业检测
- `1` - 双手持枪 (BOTH_HANDS) - 有声音
- `2` - 垂直作业面 (VERTICAL_WORKING_SURFACE) - 有声音  
- `3` - 拧紧贴合 (TIGHTEN_FIT) - 有声音
- `4` - 扳手绿灯 (GREEN_LIGHT) - 有声音
- `5` - 人员检测 (COUNT_PERSON) - 无声音
- `6` - 灯标识 (LIGHT_FLAG) - 无声音
- `7` - 全局作业结果 (GLOBAL_RESULT) - 无声音
- `8` - 手套识别 (WEAR_GLOVES) - 有声音

### 堵盖相关检测
- `21` - 堵盖大小判定 (C_SIZE) - 有声音
- `22` - 堵盖贴合判定 (C_FIT_JUDGE) - 有声音
- `23` - 堵盖手部按压判定 (C_PRESS) - 有声音

### 胶条相关检测
- `31` - 胶条安装判定 (SR_INSTALL) - 有声音
- `32` - 胶条手部按压判定 (SR_PRESS) - 有声音
- `33` - 胶条结果判定 (SR_FIT_JUDGE) - 有声音

### 管线相关检测
- `41` - 油管距离判定 (PI_OIL_DISTANCE) - 有声音
- `42` - 管路距离判定 (PI_PIPING_DISTANCE) - 有声音
- `43` - 管线-手部回拔判定 (PI_HAND_BACK) - 有声音

### 线束插接相关检测
- `51` - 线束插接-手部回拔判定 (HC_HAND_BACK) - 有声音
- `52` - 线束插接-安装结果 (HC_INSTALL) - 有声音

## 使用方法

### 基本用法

```typescript
import { useErrorSound } from '/@/composables/useErrorSound';

const { playErrorSound } = useErrorSound();

// 播放单个错误提示音
playErrorSound([{ actionType: 1 }]); // 播放"未双手作业"提示音

// 播放多个错误提示音（会播放通用的多错误提示音）
playErrorSound([
  { actionType: 1 },
  { actionType: 2 }
]);

// 混合有声音和无声音的错误（只播放有声音的错误）
playErrorSound([
  { actionType: 1 }, // 有声音
  { actionType: 5 }  // 无声音，会被过滤掉
]);
```

### 获取错误信息

```typescript
const { getErrorInfo, getAllErrorTypes, getSoundableErrorTypes } = useErrorSound();

// 获取特定错误的详细信息
const errorInfo = getErrorInfo(1);
console.log(errorInfo);
// 输出: {
//   type: 1,
//   name: 'BOTH_HANDS',
//   description: '双手持枪',
//   errorMessage: '未双手作业',
//   hasSound: true,
//   file: '/resource/error1.mp3'
// }

// 获取所有错误类型
const allTypes = getAllErrorTypes();
console.log(allTypes); // [1, 2, 3, 4, 5, 6, 7, 8, 21, 22, 23, 31, 32, 33, 41, 42, 43, 51, 52]

// 获取有声音的错误类型
const soundableTypes = getSoundableErrorTypes();
console.log(soundableTypes); // [1, 2, 3, 4, 8, 21, 22, 23, 31, 32, 33, 41, 42, 43, 51, 52]
```

## 音频文件路径

- 单个错误音频文件：`/resource/error{errorType}.mp3`
- 多个错误音频文件：`/resource/moreError.mp3`

## 特性

1. **智能过滤**：自动过滤掉没有声音的错误类型
2. **多错误处理**：当有多个需要播放声音的错误时，播放通用的多错误提示音
3. **错误处理**：音频播放失败时会在控制台输出警告信息
4. **预加载**：音频文件会被预加载以提高播放性能
5. **类型安全**：完整的 TypeScript 类型支持

## 注意事项

- 只有 `hasSound: true` 的错误类型才会播放声音
- 音频文件需要放置在 `public/resource/` 目录下
- 如果音频文件不存在，播放时会在控制台输出警告
- 音频播放需要用户交互才能正常工作（浏览器安全策略）
